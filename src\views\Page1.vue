<template>
  <div class="price-screen-container">
    <!-- 左侧价格表格区域 (70%) -->
    <div class="table-section">
      <div class="table-header">
        <h2>商品价格信息</h2>
      </div>
      <div class="table-wrapper" v-loading="loading">
        <div class="scrolling-table" ref="scrollingTable">
          <el-table
            :data="displayData"
            stripe
            :show-header="true"
            height="100%"
            class="price-table"
          >
            <el-table-column prop="code" label="编码" width="120" align="center" />
            <el-table-column prop="name" label="名称" width="200" align="center" />
            <el-table-column prop="spec" label="规格" width="150" align="center" />
            <el-table-column prop="unit" label="单位" width="80" align="center" />
            <el-table-column prop="type" label="类型" width="120" align="center" />
            <el-table-column prop="price" label="单价(元)" width="120" align="center">
              <template slot-scope="scope">
                <span class="price-text">¥{{ scope.row.price }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 右侧视频区域 (30%) -->
    <div class="video-section">
      <div class="video-wrapper">
        <video
          ref="priceVideo"
          class="price-video"
          :src="videoSrc"
          autoplay
          loop
          muted
          @error="handleVideoError"
          @loadstart="handleVideoLoadStart"
          @canplay="handleVideoCanPlay"
        >
          您的浏览器不支持视频播放
        </video>
        <div v-if="videoError" class="video-error">
          <i class="el-icon-video-camera-solid"></i>
          <p>视频加载失败</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "priceScreen",
  data() {
    return {
      loading: true,
      videoError: false,
      videoSrc: require('@/assets/videos/price_screen_video.mp4'),
      scrollTimer: null,
      scrollSpeed: 1, // 滚动速度（像素/帧）

      // 模拟商品价格数据
      priceData: [
        { code: 'MED001', name: '阿莫西林胶囊', spec: '0.25g*24粒', unit: '盒', type: '处方药', price: '15.80' },
        { code: 'MED002', name: '布洛芬缓释胶囊', spec: '0.3g*20粒', unit: '盒', type: '处方药', price: '28.50' },
        { code: 'MED003', name: '维生素C片', spec: '0.1g*100片', unit: '瓶', type: '非处方药', price: '12.30' },
        { code: 'MED004', name: '感冒灵颗粒', spec: '10g*9袋', unit: '盒', type: '非处方药', price: '18.90' },
        { code: 'MED005', name: '头孢克肟胶囊', spec: '0.1g*12粒', unit: '盒', type: '处方药', price: '45.60' },
        { code: 'MED006', name: '复方甘草片', spec: '50片', unit: '瓶', type: '处方药', price: '8.70' },
        { code: 'MED007', name: '氯雷他定片', spec: '10mg*7片', unit: '盒', type: '处方药', price: '22.40' },
        { code: 'MED008', name: '蒙脱石散', spec: '3g*10袋', unit: '盒', type: '非处方药', price: '16.80' },
        { code: 'MED009', name: '奥美拉唑肠溶胶囊', spec: '20mg*14粒', unit: '盒', type: '处方药', price: '35.20' },
        { code: 'MED010', name: '板蓝根颗粒', spec: '10g*20袋', unit: '盒', type: '非处方药', price: '13.50' },
        { code: 'MED011', name: '罗红霉素胶囊', spec: '150mg*12粒', unit: '盒', type: '处方药', price: '26.70' },
        { code: 'MED012', name: '双氯芬酸钠缓释片', spec: '75mg*10片', unit: '盒', type: '处方药', price: '19.80' },
        { code: 'MED013', name: '复合维生素B片', spec: '100片', unit: '瓶', type: '非处方药', price: '14.60' },
        { code: 'MED014', name: '硝苯地平缓释片', spec: '20mg*30片', unit: '盒', type: '处方药', price: '42.30' },
        { code: 'MED015', name: '藿香正气水', spec: '10ml*10支', unit: '盒', type: '非处方药', price: '11.90' },
        { code: 'MED016', name: '左氧氟沙星片', spec: '0.1g*10片', unit: '盒', type: '处方药', price: '38.40' },
        { code: 'MED017', name: '咳特灵胶囊', spec: '0.5g*24粒', unit: '盒', type: '非处方药', price: '21.70' },
        { code: 'MED018', name: '甲硝唑片', spec: '0.2g*100片', unit: '瓶', type: '处方药', price: '9.80' },
        { code: 'MED019', name: '健胃消食片', spec: '0.8g*36片', unit: '盒', type: '非处方药', price: '17.20' },
        { code: 'MED020', name: '阿司匹林肠溶片', spec: '25mg*30片', unit: '盒', type: '处方药', price: '6.50' }
      ]
    };
  },

  computed: {
    // 为了实现无缝循环，将数据复制一份
    displayData() {
      return [...this.priceData, ...this.priceData];
    }
  },

  mounted() {
    this.initPage();
  },

  beforeDestroy() {
    this.clearScrollTimer();
  },

  methods: {
    // 初始化页面
    async initPage() {
      // 模拟数据加载
      setTimeout(() => {
        this.loading = false;
        this.$nextTick(() => {
          this.startScrolling();
        });
      }, 1000);
    },

    // 开始滚动动画
    startScrolling() {
      const tableWrapper = this.$refs.scrollingTable;
      if (!tableWrapper) return;

      let scrollTop = 0;
      const maxScroll = tableWrapper.scrollHeight / 2; // 因为数据重复了一份，所以是一半

      const scroll = () => {
        scrollTop += this.scrollSpeed;

        // 当滚动到一半时（即原始数据的底部），重置到顶部实现无缝循环
        if (scrollTop >= maxScroll) {
          scrollTop = 0;
        }

        tableWrapper.scrollTop = scrollTop;
        this.scrollTimer = requestAnimationFrame(scroll);
      };

      this.scrollTimer = requestAnimationFrame(scroll);
    },

    // 清除滚动定时器
    clearScrollTimer() {
      if (this.scrollTimer) {
        cancelAnimationFrame(this.scrollTimer);
        this.scrollTimer = null;
      }
    },

    // 视频错误处理
    handleVideoError() {
      this.videoError = true;
      console.error('视频加载失败');
    },

    // 视频开始加载
    handleVideoLoadStart() {
      this.videoError = false;
    },

    // 视频可以播放
    handleVideoCanPlay() {
      this.videoError = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.price-screen-container {
  display: flex;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;

  // 左侧表格区域 (70%)
  .table-section {
    width: 70%;
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .table-header {
      margin-bottom: 20px;
      text-align: center;

      h2 {
        color: #ffffff;
        font-size: 28px;
        font-weight: bold;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }
    }

    .table-wrapper {
      flex: 1;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      overflow: hidden;
      position: relative;

      .scrolling-table {
        height: 100%;
        overflow: hidden;

        .price-table {
          width: 100%;

          // 表格头部样式
          ::v-deep .el-table__header-wrapper {
            .el-table__header {
              th {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: #ffffff;
                font-weight: bold;
                font-size: 16px;
                text-align: center;
                border: none;

                .cell {
                  padding: 15px 8px;
                }
              }
            }
          }

          // 表格主体样式
          ::v-deep .el-table__body-wrapper {
            .el-table__body {
              tr {
                transition: all 0.3s ease;

                &:hover {
                  background-color: rgba(74, 172, 254, 0.1) !important;
                }

                td {
                  border: none;
                  font-size: 14px;
                  color: #333;

                  .cell {
                    padding: 12px 8px;
                    text-align: center;
                  }

                  .price-text {
                    font-weight: bold;
                    color: #e74c3c;
                    font-size: 16px;
                  }
                }

                // 斑马纹样式
                &.el-table__row--striped {
                  td {
                    background-color: rgba(74, 172, 254, 0.05) !important;
                  }
                }
              }
            }
          }

          // 移除表格边框
          ::v-deep .el-table {
            border: none;

            &::before {
              display: none;
            }

            &::after {
              display: none;
            }
          }

          ::v-deep .el-table__border-left-patch {
            display: none;
          }
        }
      }
    }
  }

  // 右侧视频区域 (30%)
  .video-section {
    width: 30%;
    height: 100%;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    .video-wrapper {
      width: 100%;
      height: 80%;
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      background: rgba(0, 0, 0, 0.1);

      .price-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
      }

      .video-error {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.7);
        color: #ffffff;
        border-radius: 12px;

        i {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.7;
        }

        p {
          font-size: 16px;
          margin: 0;
          opacity: 0.9;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .price-screen-container {
    .table-section {
      .table-header {
        h2 {
          font-size: 24px;
        }
      }

      .table-wrapper {
        .scrolling-table {
          .price-table {
            ::v-deep .el-table__header-wrapper {
              .el-table__header {
                th {
                  font-size: 14px;

                  .cell {
                    padding: 12px 6px;
                  }
                }
              }
            }

            ::v-deep .el-table__body-wrapper {
              .el-table__body {
                tr {
                  td {
                    font-size: 13px;

                    .cell {
                      padding: 10px 6px;
                    }

                    .price-text {
                      font-size: 14px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .price-screen-container {
    flex-direction: column;

    .table-section {
      width: 100%;
      height: 70%;
      padding: 15px;
    }

    .video-section {
      width: 100%;
      height: 30%;
      padding: 15px;

      .video-wrapper {
        height: 100%;
      }
    }
  }
}

// Loading 样式覆盖
::v-deep .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);

  .el-loading-spinner {
    .el-loading-text {
      color: #4facfe;
      font-weight: bold;
    }

    .circular {
      color: #4facfe;
    }
  }
}
</style>