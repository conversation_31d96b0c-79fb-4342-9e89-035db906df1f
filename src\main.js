import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import autofit from "autofit.js";

import "normalize.css";
import "@/styles/index.scss"; // global css

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

Vue.config.productionTip = false;

// 使用Element UI
Vue.use(ElementUI);

new Vue({
  router,
  render: h => h(App),
}).$mount("#app");

// 初始化autofit.js进行屏幕适配
autofit.init({
  dh: 1080,
  dw: 1920,
  el: "#app",
  resize: true,
});
