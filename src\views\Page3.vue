<template>
  <div class="page-container">
    <h1>页面3</h1>
    <p>这是页面3的内容，用于展示大屏数据可视化内容。</p>
    <div class="content-area">
      <p>在这里可以添加图表、数据展示等内容</p>
    </div>
    <router-link to="/" class="back-btn">返回首页</router-link>
  </div>
</template>

<script>
export default {
  name: 'Page3',
  data() {
    return {
      // 页面数据
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

h1 {
  font-size: 48px;
  margin-bottom: 20px;
}

.content-area {
  margin: 40px 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.back-btn {
  display: inline-block;
  padding: 10px 20px;
  background: #fff;
  color: #333;
  text-decoration: none;
  border-radius: 5px;
  margin-top: 20px;
}

.back-btn:hover {
  background: #f0f0f0;
}
</style>
